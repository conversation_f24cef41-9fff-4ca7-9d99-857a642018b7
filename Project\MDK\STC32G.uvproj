<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_proj.xsd">

  <SchemaVersion>1.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>STC32G12K128_BLDC_HALLESS</TargetName>
      <ToolsetNumber>0x1</ToolsetNumber>
      <ToolsetName>MCS-251</ToolsetName>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STC32G12K128 Series</Device>
          <Vendor>STC</Vendor>
          <Cpu>IRAM(0-0xFFF) XRAM(0x10000-0x11FFF) IROM(0xFE0000-0xFFFFFF) CLOCK(35000000)</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile>"LIB\STARTUP251.ASM" ("80251 Startup Code")</StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId>63457</DeviceId>
          <RegisterFile>STC16F.H</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath>STC\</RegisterFilePath>
          <DBRegisterFilePath>STC\</DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Out_File\</OutputDirectory>
          <OutputName>SEEKFREE</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Out_File\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
          <BankNo>65535</BankNo>
        </CommonProperty>
        <DllOption>
          <SimDllName>S251.DLL</SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll>DCORE51.DLL</SimDlgDll>
          <SimDlgDllArguments>-p251</SimDlgDllArguments>
          <TargetDllName>S251.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCORE51.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-p251</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>0</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>1</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>0</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>BIN\MON251.DLL</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>0</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>0</UpdateFlashBeforeDebugging>
            <Capability>0</Capability>
            <DriverSelection>-1</DriverSelection>
          </Flash1>
          <bUseTDR>0</bUseTDR>
          <Flash2></Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <Target251>
          <Target251Misc>
            <MemoryModel>3</MemoryModel>
            <RTOS>0</RTOS>
            <RomSize>3</RomSize>
            <NearDataHold>0</NearDataHold>
            <XDataHold>0</XDataHold>
            <FarDataHold>0</FarDataHold>
            <uocRom>0</uocRom>
            <uocXRAM>0</uocXRAM>
            <uSrcBin>1</uSrcBin>
            <uFrame4>0</uFrame4>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>1</hadXRAM>
            <hadIROM>1</hadIROM>
            <Use_Code_Banking>0</Use_Code_Banking>
            <uCC7>0</uCC7>
            <fp_hp>0</fp_hp>
            <CBANKS2>0</CBANKS2>
            <OnChipMemories>
              <RCB>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x10000</Size>
              </RCB>
              <IROM>
                <Type>1</Type>
                <StartAddress>0xfe0000</StartAddress>
                <Size>0x20000</Size>
              </IROM>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x1000</Size>
              </IRAM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x10000</StartAddress>
                <Size>0x2000</Size>
              </XRAM>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
            </OnChipMemories>
          </Target251Misc>
          <C251>
            <RegColor>0</RegColor>
            <uOrder>0</uOrder>
            <uAlias>1</uAlias>
            <uRentF>0</uRentF>
            <uUch>1</uUch>
            <uFlt64>0</uFlt64>
            <Fuzzy>3</Fuzzy>
            <Optim>7</Optim>
            <wLevel>3</wLevel>
            <SizSpd>1</SizSpd>
            <AcaOpt>0</AcaOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath>..\..\Libraries\libraries;..\..\Libraries\seekfree_libraries;..\..\Libraries\seekfree_peripheral;..\..\bldc;..\CODE;..\USER\inc;..\USER\src</IncludePath>
            </VariousControls>
          </C251>
          <Ax51>
            <UseMpl>0</UseMpl>
            <UseStandard>1</UseStandard>
            <UseCase>0</UseCase>
            <UseMod51>0</UseMod51>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Ax51>
          <Lx51>
            <useFile>0</useFile>
            <linkonly>0</linkonly>
            <UseMemoryFromTarget>1</UseMemoryFromTarget>
            <CaseSensitiveSymbols>1</CaseSensitiveSymbols>
            <WarningLevel>2</WarningLevel>
            <DataOverlaying>1</DataOverlaying>
            <OverlayString></OverlayString>
            <MiscControls></MiscControls>
            <DisableWarningNumbers>16,57</DisableWarningNumbers>
            <LinkerCmdFile></LinkerCmdFile>
            <Assign></Assign>
            <ReserveString></ReserveString>
            <CClasses></CClasses>
            <UserClasses></UserClasses>
            <CSection></CSection>
            <UserSection></UserSection>
            <CodeBaseAddress></CodeBaseAddress>
            <XDataBaseAddress></XDataBaseAddress>
            <PDataBaseAddress></PDataBaseAddress>
            <BitBaseAddress></BitBaseAddress>
            <DataBaseAddress></DataBaseAddress>
            <IDataBaseAddress></IDataBaseAddress>
            <Precede></Precede>
            <Stack></Stack>
            <CodeSegmentName></CodeSegmentName>
            <XDataSegmentName></XDataSegmentName>
            <BitSegmentName></BitSegmentName>
            <DataSegmentName></DataSegmentName>
            <IDataSegmentName></IDataSegmentName>
          </Lx51>
        </Target251>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>startup</GroupName>
          <Files>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\libraries\board.c</FilePath>
            </File>
            <File>
              <FileName>board.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\libraries\board.h</FilePath>
            </File>
            <File>
              <FileName>common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\libraries\common.c</FilePath>
            </File>
            <File>
              <FileName>common.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\libraries\common.h</FilePath>
            </File>
            <File>
              <FileName>STC32Gxx.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\libraries\STC32Gxx.h</FilePath>
            </File>
            <File>
              <FileName>START251.A51</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\Libraries\libraries\START251.A51</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>seekfree_libraries</GroupName>
          <Files>
            <File>
              <FileName>headfile.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\headfile.h</FilePath>
            </File>
            <File>
              <FileName>zf_delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_delay.c</FilePath>
            </File>
            <File>
              <FileName>zf_delay.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_delay.h</FilePath>
            </File>
            <File>
              <FileName>zf_pwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_pwm.c</FilePath>
            </File>
            <File>
              <FileName>zf_pwm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_pwm.h</FilePath>
            </File>
            <File>
              <FileName>zf_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_uart.c</FilePath>
            </File>
            <File>
              <FileName>zf_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_uart.h</FilePath>
            </File>
            <File>
              <FileName>zf_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_gpio.c</FilePath>
            </File>
            <File>
              <FileName>zf_gpio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_gpio.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>doc</GroupName>
          <Files>
            <File>
              <FileName>version.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\doc\version.txt</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_c</GroupName>
          <Files>
            <File>
              <FileName>isr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USER\src\isr.c</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USER\src\main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_h</GroupName>
          <Files>
            <File>
              <FileName>isr.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\USER\inc\isr.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>bldc</GroupName>
          <Files>
            <File>
              <FileName>comparator.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\bldc\comparator.c</FilePath>
            </File>
            <File>
              <FileName>comparator.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\bldc\comparator.h</FilePath>
            </File>
            <File>
              <FileName>pit_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\bldc\pit_timer.c</FilePath>
            </File>
            <File>
              <FileName>pit_timer.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\bldc\pit_timer.h</FilePath>
            </File>
            <File>
              <FileName>battery.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\bldc\battery.c</FilePath>
            </File>
            <File>
              <FileName>battery.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\bldc\battery.h</FilePath>
            </File>
            <File>
              <FileName>motor_control.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\bldc\motor_control.c</FilePath>
            </File>
            <File>
              <FileName>motor_control.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\bldc\motor_control.h</FilePath>
            </File>
            <File>
              <FileName>sine_control.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\bldc\sine_control.c</FilePath>
            </File>
            <File>
              <FileName>sine_control.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\bldc\sine_control.h</FilePath>
            </File>
            <File>
              <FileName>pwm_out.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\bldc\pwm_out.c</FilePath>
            </File>
            <File>
              <FileName>pwm_out.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\bldc\pwm_out.h</FilePath>
            </File>
            <File>
              <FileName>signal_input.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\bldc\signal_input.c</FilePath>
            </File>
            <File>
              <FileName>signal_input.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\bldc\signal_input.h</FilePath>
            </File>
            <File>
              <FileName>bldc_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\bldc\bldc_config.h</FilePath>
            </File>
            <File>
              <FileName>bldc_version.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\bldc\bldc_version.txt</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

</Project>
